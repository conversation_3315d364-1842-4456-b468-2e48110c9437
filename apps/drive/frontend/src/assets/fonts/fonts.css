/* IBM Plex Sans */
@font-face {
  font-family: "IBM Plex Sans";
  src: url("./IBM_Plex_Sans/IBMPlexSans-VariableFont_wdth,wght.ttf")
    format("truetype");
  font-weight: 100 700;
  font-stretch: 75% 100%;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "IBM Plex Sans";
  src: url("./IBM_Plex_Sans/IBMPlexSans-Italic-VariableFont_wdth,wght.ttf")
    format("truetype");
  font-weight: 100 700;
  font-stretch: 75% 100%;
  font-style: italic;
  font-display: swap;
}

/* <PERSON><PERSON> */
@font-face {
  font-family: "EB Garamond";
  src: url("./EB_Garamond/EBGaramond-VariableFont_wght.ttf") format("truetype");
  font-weight: 400 800;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "E<PERSON> Garamond";
  src: url("./E<PERSON>_Garamond/EBGaramond-Italic-VariableFont_wght.ttf")
    format("truetype");
  font-weight: 400 800;
  font-style: italic;
  font-display: swap;
}

/* Comfortaa */
@font-face {
  font-family: "Comfortaa";
  src: url("./Comfortaa/Comfortaa-VariableFont_wght.ttf") format("truetype");
  font-weight: 300 700;
  font-style: normal;
  font-display: swap;
}

/* Cormorant */
@font-face {
  font-family: "Cormorant";
  src: url("./Cormorant/Cormorant-VariableFont_wght.ttf") format("truetype");
  font-weight: 300 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Cormorant";
  src: url("./Cormorant/Cormorant-Italic-VariableFont_wght.ttf")
    format("truetype");
  font-weight: 300 700;
  font-style: italic;
  font-display: swap;
}

/* Merriweather */
@font-face {
  font-family: "Merriweather";
  src: url("./Merriweather/Merriweather-VariableFont_opsz,wdth,wght.ttf")
    format("truetype");
  font-style: normal;
  font-weight: 300 900;
  font-display: swap;
  font-stretch: normal;
}

/* Merriweather Italic */
@font-face {
  font-family: "Merriweather";
  src: url("./Merriweather/Merriweather-Italic-VariableFont_opsz,wdth,wght.ttf")
    format("truetype");
  font-style: italic;
  font-weight: 300 900;
  font-display: swap;
  font-stretch: normal;
}

/* Lora */
@font-face {
  font-family: "Lora";
  src: url("./Lora/Lora-VariableFont_wght.ttf") format("truetype");
  font-weight: 400 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Lora";
  src: url("./Lora/Lora-Italic-VariableFont_wght.ttf") format("truetype");
  font-weight: 400 700;
  font-style: italic;
  font-display: swap;
}

/* Nunito */
@font-face {
  font-family: "Nunito";
  src: url("./Nunito/Nunito-VariableFont_wght.ttf") format("truetype");
  font-weight: 200 1000;
  font-style: normal;
  font-display: swap;
}

/* Roboto */
@font-face {
  font-family: "Roboto";
  src: url("./Roboto/Roboto-VariableFont_wght.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roboto";
  src: url("./Roboto/Roboto-Italic-VariableFont_wght.ttf") format("truetype");
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Geist */
@font-face {
  font-family: "Geist";
  src: url("./Geist/Geist-Variable.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Geist";
  src: url("./Geist/Geist-VariableItalic.ttf") format("truetype");
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Caveat */
@font-face {
  font-family: "Caveat";
  src: url("./Caveat/Caveat-VariableFont_wght.ttf") format("truetype");
  font-weight: 400 700;
  font-style: normal;
  font-display: swap;
}
